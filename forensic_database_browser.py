#!/usr/bin/env python3
"""
Forensic Database Browser and Query Tool
Interactive tool to explore and query the forensic SQLite database
Provides legal-compliant data access and reporting capabilities
"""

import sqlite3
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple
import csv

class ForensicDatabaseBrowser:
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path)
        self.forensic_db_path = self.workspace_path / "MASTER_FORENSIC_DATABASE.db"
        
        if not self.forensic_db_path.exists():
            raise FileNotFoundError(f"Forensic database not found: {self.forensic_db_path}")
    
    def get_database_schema(self) -> Dict[str, List[Dict]]:
        """Get complete database schema information"""
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        schema = {}
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall()]
        
        for table in tables:
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            row_count = cursor.fetchone()[0]
            
            schema[table] = {
                'columns': [
                    {
                        'id': col[0],
                        'name': col[1],
                        'type': col[2],
                        'not_null': bool(col[3]),
                        'default_value': col[4],
                        'primary_key': bool(col[5])
                    }
                    for col in columns
                ],
                'row_count': row_count
            }
        
        conn.close()
        return schema
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute SQL query and return results"""
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # Get column names
            columns = [description[0] for description in cursor.description] if cursor.description else []
            
            # Fetch results
            rows = cursor.fetchall()
            
            # Convert to list of dictionaries
            results = [dict(zip(columns, row)) for row in rows]
            
            conn.close()
            return results
            
        except Exception as e:
            conn.close()
            raise e
    
    def get_case_summary(self) -> List[Dict[str, Any]]:
        """Get comprehensive case summary"""
        query = """
        SELECT 
            c.case_id,
            c.case_number,
            c.case_title,
            c.case_type,
            c.investigating_officer,
            c.police_station,
            c.case_status,
            c.fir_number,
            c.fir_date,
            COUNT(DISTINCT e.evidence_id) as total_evidence,
            COUNT(DISTINCT da.db_analysis_id) as databases_analyzed,
            COUNT(DISTINCT fe.examination_id) as forensic_examinations,
            COUNT(DISTINCT fr.report_id) as reports_generated
        FROM cases c
        LEFT JOIN evidence_catalog e ON c.case_id = e.case_id
        LEFT JOIN analyzed_databases da ON c.case_id = da.case_id
        LEFT JOIN forensic_examinations fe ON c.case_id = fe.case_id
        LEFT JOIN forensic_reports fr ON c.case_id = fr.case_id
        GROUP BY c.case_id
        """
        return self.execute_query(query)
    
    def get_database_analysis_summary(self) -> List[Dict[str, Any]]:
        """Get database analysis summary"""
        query = """
        SELECT 
            da.database_name,
            da.file_size_mb,
            da.analysis_date,
            da.total_tables,
            da.total_records,
            da.file_hash_sha256,
            da.examiner_name,
            COUNT(dt.table_id) as analyzed_tables
        FROM analyzed_databases da
        LEFT JOIN database_tables dt ON da.db_analysis_id = dt.db_analysis_id
        GROUP BY da.db_analysis_id
        ORDER BY da.file_size_mb DESC
        """
        return self.execute_query(query)
    
    def get_legal_procedures(self) -> List[Dict[str, Any]]:
        """Get all legal procedures"""
        query = """
        SELECT 
            procedure_id,
            procedure_name,
            legal_section,
            act_reference,
            procedure_category,
            required_documentation,
            responsible_authority
        FROM legal_procedures
        ORDER BY act_reference, legal_section
        """
        return self.execute_query(query)
    
    def get_table_details(self, table_name: str) -> Dict[str, Any]:
        """Get detailed information about a specific table"""
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        # Get table schema
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # Get sample data (first 10 rows)
        cursor.execute(f"SELECT * FROM {table_name} LIMIT 10")
        sample_data = cursor.fetchall()
        column_names = [description[0] for description in cursor.description]
        
        # Get row count
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        row_count = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'table_name': table_name,
            'columns': [
                {
                    'id': col[0],
                    'name': col[1],
                    'type': col[2],
                    'not_null': bool(col[3]),
                    'default_value': col[4],
                    'primary_key': bool(col[5])
                }
                for col in columns
            ],
            'row_count': row_count,
            'sample_data': [dict(zip(column_names, row)) for row in sample_data]
        }
    
    def search_databases_by_criteria(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search databases by various criteria"""
        where_clauses = []
        params = []
        
        if criteria.get('min_size_mb'):
            where_clauses.append("file_size_mb >= ?")
            params.append(criteria['min_size_mb'])
        
        if criteria.get('max_size_mb'):
            where_clauses.append("file_size_mb <= ?")
            params.append(criteria['max_size_mb'])
        
        if criteria.get('database_name_pattern'):
            where_clauses.append("database_name LIKE ?")
            params.append(f"%{criteria['database_name_pattern']}%")
        
        if criteria.get('min_tables'):
            where_clauses.append("total_tables >= ?")
            params.append(criteria['min_tables'])
        
        if criteria.get('min_records'):
            where_clauses.append("total_records >= ?")
            params.append(criteria['min_records'])
        
        where_clause = " AND ".join(where_clauses) if where_clauses else "1=1"
        
        query = f"""
        SELECT 
            database_name,
            database_path,
            file_size_mb,
            total_tables,
            total_records,
            analysis_date,
            file_hash_sha256
        FROM analyzed_databases
        WHERE {where_clause}
        ORDER BY file_size_mb DESC
        """
        
        return self.execute_query(query, tuple(params))
    
    def export_table_to_csv(self, table_name: str, output_file: str = None) -> str:
        """Export table data to CSV file"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"{table_name}_export_{timestamp}.csv"
        
        output_path = self.workspace_path / output_file
        
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        # Get all data from table
        cursor.execute(f"SELECT * FROM {table_name}")
        rows = cursor.fetchall()
        column_names = [description[0] for description in cursor.description]
        
        # Write to CSV
        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(column_names)  # Header
            writer.writerows(rows)
        
        conn.close()
        return str(output_path)
    
    def generate_forensic_summary_report(self) -> str:
        """Generate comprehensive forensic summary report"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.workspace_path / f"forensic_summary_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("FORENSIC DATABASE SUMMARY REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S IST')}\n")
            f.write(f"Database: {self.forensic_db_path}\n\n")
            
            # Database schema summary
            schema = self.get_database_schema()
            f.write(f"TOTAL TABLES: {len(schema)}\n")
            f.write(f"TOTAL RECORDS: {sum(table['row_count'] for table in schema.values())}\n\n")
            
            # Case summary
            cases = self.get_case_summary()
            f.write("CASE SUMMARY:\n")
            f.write("-" * 20 + "\n")
            for case in cases:
                f.write(f"Case: {case['case_number']} - {case['case_title']}\n")
                f.write(f"Status: {case['case_status']}\n")
                f.write(f"IO: {case['investigating_officer']}\n")
                f.write(f"Databases Analyzed: {case['databases_analyzed']}\n")
                f.write(f"Evidence Items: {case['total_evidence']}\n\n")
            
            # Database analysis summary
            db_analysis = self.get_database_analysis_summary()
            f.write("DATABASE ANALYSIS SUMMARY:\n")
            f.write("-" * 30 + "\n")
            for db in db_analysis:
                f.write(f"Database: {db['database_name']}\n")
                f.write(f"Size: {db['file_size_mb']:.2f} MB\n")
                f.write(f"Tables: {db['total_tables']}\n")
                f.write(f"Records: {db['total_records']:,}\n")
                f.write(f"Hash: {db['file_hash_sha256'][:32]}...\n\n")
            
            # Legal procedures
            procedures = self.get_legal_procedures()
            f.write("LEGAL PROCEDURES:\n")
            f.write("-" * 20 + "\n")
            for proc in procedures:
                f.write(f"Procedure: {proc['procedure_name']}\n")
                f.write(f"Legal Section: {proc['legal_section']}\n")
                f.write(f"Act: {proc['act_reference']}\n")
                f.write(f"Authority: {proc['responsible_authority']}\n\n")
            
            # Table statistics
            f.write("TABLE STATISTICS:\n")
            f.write("-" * 20 + "\n")
            for table_name, table_info in schema.items():
                f.write(f"{table_name}: {table_info['row_count']:,} records\n")
        
        return str(report_file)
    
    def interactive_query_interface(self):
        """Interactive command-line interface for querying the database"""
        print("🔍 FORENSIC DATABASE BROWSER")
        print("=" * 40)
        print(f"Database: {self.forensic_db_path}")
        print(f"Size: {self.forensic_db_path.stat().st_size / (1024*1024):.2f} MB")
        print("\nAvailable Commands:")
        print("1. schema - Show database schema")
        print("2. cases - Show case summary")
        print("3. databases - Show database analysis")
        print("4. procedures - Show legal procedures")
        print("5. table <name> - Show table details")
        print("6. query <sql> - Execute custom SQL query")
        print("7. export <table> - Export table to CSV")
        print("8. search - Search databases by criteria")
        print("9. report - Generate summary report")
        print("10. quit - Exit browser")
        print()
        
        while True:
            try:
                command = input("forensic_db> ").strip().lower()
                
                if command == "quit" or command == "exit":
                    break
                elif command == "schema":
                    schema = self.get_database_schema()
                    for table_name, table_info in schema.items():
                        print(f"{table_name}: {table_info['row_count']:,} records")
                
                elif command == "cases":
                    cases = self.get_case_summary()
                    for case in cases:
                        print(f"Case: {case['case_number']} - {case['case_title']}")
                        print(f"  Status: {case['case_status']}")
                        print(f"  Databases: {case['databases_analyzed']}")
                        print(f"  Evidence: {case['total_evidence']}")
                
                elif command == "databases":
                    dbs = self.get_database_analysis_summary()
                    for db in dbs:
                        print(f"Database: {db['database_name']}")
                        print(f"  Size: {db['file_size_mb']:.2f} MB")
                        print(f"  Tables: {db['total_tables']}, Records: {db['total_records']:,}")
                
                elif command == "procedures":
                    procedures = self.get_legal_procedures()
                    for proc in procedures:
                        print(f"Procedure: {proc['procedure_name']}")
                        print(f"  Section: {proc['legal_section']} ({proc['act_reference']})")
                
                elif command.startswith("table "):
                    table_name = command.split(" ", 1)[1]
                    try:
                        details = self.get_table_details(table_name)
                        print(f"Table: {details['table_name']}")
                        print(f"Rows: {details['row_count']:,}")
                        print("Columns:")
                        for col in details['columns']:
                            pk = " (PK)" if col['primary_key'] else ""
                            nn = " NOT NULL" if col['not_null'] else ""
                            print(f"  {col['name']}: {col['type']}{pk}{nn}")
                    except Exception as e:
                        print(f"Error: {e}")
                
                elif command.startswith("query "):
                    sql = command.split(" ", 1)[1]
                    try:
                        results = self.execute_query(sql)
                        if results:
                            # Print first few results
                            for i, row in enumerate(results[:5]):
                                print(f"Row {i+1}: {row}")
                            if len(results) > 5:
                                print(f"... and {len(results)-5} more rows")
                        else:
                            print("No results returned")
                    except Exception as e:
                        print(f"Query error: {e}")
                
                elif command.startswith("export "):
                    table_name = command.split(" ", 1)[1]
                    try:
                        output_file = self.export_table_to_csv(table_name)
                        print(f"Table exported to: {output_file}")
                    except Exception as e:
                        print(f"Export error: {e}")
                
                elif command == "search":
                    print("Search criteria (press Enter to skip):")
                    criteria = {}
                    
                    min_size = input("Minimum size (MB): ").strip()
                    if min_size:
                        criteria['min_size_mb'] = float(min_size)
                    
                    name_pattern = input("Database name pattern: ").strip()
                    if name_pattern:
                        criteria['database_name_pattern'] = name_pattern
                    
                    min_tables = input("Minimum tables: ").strip()
                    if min_tables:
                        criteria['min_tables'] = int(min_tables)
                    
                    results = self.search_databases_by_criteria(criteria)
                    for db in results:
                        print(f"Database: {db['database_name']}")
                        print(f"  Size: {db['file_size_mb']:.2f} MB")
                        print(f"  Tables: {db['total_tables']}, Records: {db['total_records']:,}")
                
                elif command == "report":
                    report_file = self.generate_forensic_summary_report()
                    print(f"Summary report generated: {report_file}")
                
                else:
                    print("Unknown command. Type 'quit' to exit.")
                
                print()  # Empty line for readability
                
            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except Exception as e:
                print(f"Error: {e}")

def main():
    """Main function to start the forensic database browser"""
    workspace = r"c:\Users\<USER>\Desktop\New folder"
    
    try:
        browser = ForensicDatabaseBrowser(workspace)
        
        # Generate initial summary report
        print("📊 Generating initial forensic summary report...")
        report_file = browser.generate_forensic_summary_report()
        print(f"✅ Summary report generated: {report_file}")
        
        # Start interactive interface
        browser.interactive_query_interface()
        
    except FileNotFoundError as e:
        print(f"❌ {e}")
        print("Please run create_forensic_sqlite_database.py first to create the forensic database.")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

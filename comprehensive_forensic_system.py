#!/usr/bin/env python3
"""
Comprehensive Forensic Database Management System
Complete solution for Indian Legal System compliance
Includes image extraction, evidence cataloging, and legal procedures
"""

import sqlite3
import json
import os
import hashlib
import base64
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple
import shutil

class ComprehensiveForensicSystem:
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path)
        self.forensic_db_path = self.workspace_path / "comprehensive_forensic_evidence.db"
        self.evidence_dir = self.workspace_path / "forensic_evidence"
        self.images_dir = self.evidence_dir / "images"
        self.documents_dir = self.evidence_dir / "documents"
        self.reports_dir = self.evidence_dir / "reports"
        
        # Create directories
        for directory in [self.evidence_dir, self.images_dir, self.documents_dir, self.reports_dir]:
            directory.mkdir(exist_ok=True)
        
        self.init_forensic_database()
    
    def init_forensic_database(self):
        """Initialize the comprehensive forensic database"""
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        # Evidence catalog table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS evidence_catalog (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            evidence_id TEXT UNIQUE NOT NULL,
            evidence_type TEXT NOT NULL,
            source_database TEXT,
            source_table TEXT,
            description TEXT,
            file_path TEXT,
            file_hash_md5 TEXT,
            file_hash_sha256 TEXT,
            file_size_bytes INTEGER,
            created_timestamp TEXT,
            collected_by TEXT,
            chain_of_custody TEXT,
            legal_relevance TEXT,
            admissibility_status TEXT,
            section_65b_compliance TEXT
        )''')
        
        # Digital images table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS digital_images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            image_id TEXT UNIQUE NOT NULL,
            evidence_id TEXT,
            image_name TEXT,
            image_path TEXT,
            image_format TEXT,
            image_size_bytes INTEGER,
            image_hash TEXT,
            extraction_method TEXT,
            metadata_json TEXT,
            timestamp_extracted TEXT,
            FOREIGN KEY (evidence_id) REFERENCES evidence_catalog (evidence_id)
        )''')
        
        # Legal procedures table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS legal_procedures (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            procedure_id TEXT UNIQUE NOT NULL,
            procedure_name TEXT,
            legal_section TEXT,
            act_reference TEXT,
            procedure_steps TEXT,
            compliance_checklist TEXT,
            required_documentation TEXT,
            timeline_requirements TEXT,
            responsible_authority TEXT
        )''')
        
        # Chain of custody table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS chain_of_custody (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            custody_id TEXT UNIQUE NOT NULL,
            evidence_id TEXT,
            handler_name TEXT,
            handler_designation TEXT,
            action_taken TEXT,
            timestamp TEXT,
            location TEXT,
            purpose TEXT,
            next_handler TEXT,
            digital_signature TEXT,
            FOREIGN KEY (evidence_id) REFERENCES evidence_catalog (evidence_id)
        )''')
        
        # Legal compliance tracking
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS compliance_tracking (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            compliance_id TEXT UNIQUE NOT NULL,
            evidence_id TEXT,
            legal_requirement TEXT,
            compliance_status TEXT,
            verification_method TEXT,
            verifier_name TEXT,
            verification_timestamp TEXT,
            notes TEXT,
            FOREIGN KEY (evidence_id) REFERENCES evidence_catalog (evidence_id)
        )''')
        
        conn.commit()
        conn.close()
        
        # Insert standard legal procedures
        self.insert_standard_procedures()
    
    def insert_standard_procedures(self):
        """Insert standard Indian legal procedures"""
        procedures = [
            {
                'procedure_id': 'IEA_65B_COMPLIANCE',
                'procedure_name': 'Section 65B Indian Evidence Act Compliance',
                'legal_section': 'Section 65B',
                'act_reference': 'Indian Evidence Act, 1872',
                'procedure_steps': json.dumps([
                    "1. Identify the electronic device/system containing the evidence",
                    "2. Ensure the device was in regular use during the relevant period",
                    "3. Verify that information was regularly fed into the computer",
                    "4. Confirm the computer was operating properly",
                    "5. Ensure the information derives from the original source",
                    "6. Obtain certificate under Section 65B(4)",
                    "7. Document the chain of custody",
                    "8. Preserve original evidence without modification"
                ]),
                'compliance_checklist': json.dumps([
                    "Certificate under Section 65B(4) obtained",
                    "Chain of custody documented",
                    "Hash values calculated and verified",
                    "Original evidence preserved",
                    "Proper authorization obtained",
                    "Expert witness identified if required"
                ]),
                'required_documentation': 'Section 65B Certificate, Chain of Custody, Hash Verification Report',
                'timeline_requirements': 'Evidence must be collected and certified within reasonable time',
                'responsible_authority': 'Investigating Officer / Forensic Expert'
            },
            {
                'procedure_id': 'BSA_2023_DIGITAL_EVIDENCE',
                'procedure_name': 'Bharatiya Sakshya Adhiniyam 2023 Digital Evidence',
                'legal_section': 'Section 63',
                'act_reference': 'Bharatiya Sakshya Adhiniyam, 2023',
                'procedure_steps': json.dumps([
                    "1. Identify electronic or digital record",
                    "2. Ensure proper authentication of digital evidence",
                    "3. Verify integrity through hash functions",
                    "4. Document the source and method of collection",
                    "5. Maintain chain of custody",
                    "6. Obtain necessary certificates",
                    "7. Ensure compliance with procedural requirements"
                ]),
                'compliance_checklist': json.dumps([
                    "Digital record properly authenticated",
                    "Integrity verified through cryptographic methods",
                    "Source documentation complete",
                    "Chain of custody maintained",
                    "Procedural compliance verified"
                ]),
                'required_documentation': 'Authentication Certificate, Integrity Verification, Chain of Custody',
                'timeline_requirements': 'As per investigation timeline',
                'responsible_authority': 'Investigating Officer / Digital Forensic Expert'
            },
            {
                'procedure_id': 'BNSS_2023_SEARCH_SEIZURE',
                'procedure_name': 'BNSS 2023 Search and Seizure of Digital Evidence',
                'legal_section': 'Section 93, 100',
                'act_reference': 'Bharatiya Nagarik Suraksha Sanhita, 2023',
                'procedure_steps': json.dumps([
                    "1. Obtain proper search warrant or authorization",
                    "2. Conduct search in presence of witnesses",
                    "3. Prepare detailed seizure memo",
                    "4. Create forensic images of digital devices",
                    "5. Calculate and record hash values",
                    "6. Seal and label evidence properly",
                    "7. Update investigation diary",
                    "8. Submit to Forensic Science Laboratory if required"
                ]),
                'compliance_checklist': json.dumps([
                    "Valid search warrant obtained",
                    "Witnesses present during search",
                    "Seizure memo prepared",
                    "Forensic imaging completed",
                    "Hash values calculated",
                    "Evidence properly sealed",
                    "Investigation diary updated"
                ]),
                'required_documentation': 'Search Warrant, Seizure Memo, Witness Statements, Hash Report',
                'timeline_requirements': 'Search to be completed within authorized timeframe',
                'responsible_authority': 'Investigating Officer with Forensic Expert'
            }
        ]
        
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        for procedure in procedures:
            cursor.execute('''
            INSERT OR REPLACE INTO legal_procedures 
            (procedure_id, procedure_name, legal_section, act_reference, 
             procedure_steps, compliance_checklist, required_documentation, 
             timeline_requirements, responsible_authority)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                procedure['procedure_id'],
                procedure['procedure_name'],
                procedure['legal_section'],
                procedure['act_reference'],
                procedure['procedure_steps'],
                procedure['compliance_checklist'],
                procedure['required_documentation'],
                procedure['timeline_requirements'],
                procedure['responsible_authority']
            ))
        
        conn.commit()
        conn.close()
    
    def extract_images_from_databases(self) -> List[Dict[str, Any]]:
        """Extract images and binary data from all databases"""
        extracted_images = []
        db_files = list(self.workspace_path.rglob('*.db')) + list(self.workspace_path.rglob('*.sqlite'))
        
        for db_file in db_files:
            try:
                conn = sqlite3.connect(str(db_file))
                cursor = conn.cursor()
                
                # Get all tables
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                for table_name in [t[0] for t in tables]:
                    # Get table schema
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    
                    # Look for BLOB columns or columns that might contain images
                    blob_columns = []
                    for col in columns:
                        col_name, col_type = col[1], col[2]
                        if ('BLOB' in col_type.upper() or 
                            'image' in col_name.lower() or 
                            'photo' in col_name.lower() or
                            'picture' in col_name.lower() or
                            'binary' in col_name.lower()):
                            blob_columns.append(col_name)
                    
                    if blob_columns:
                        # Extract data from BLOB columns
                        for blob_col in blob_columns:
                            try:
                                cursor.execute(f"SELECT rowid, {blob_col} FROM {table_name} WHERE {blob_col} IS NOT NULL")
                                blob_data = cursor.fetchall()
                                
                                for row_id, blob_content in blob_data:
                                    if blob_content and len(blob_content) > 100:  # Minimum size check
                                        image_info = self.save_extracted_image(
                                            blob_content, db_file, table_name, blob_col, row_id
                                        )
                                        if image_info:
                                            extracted_images.append(image_info)
                            except Exception as e:
                                print(f"Error extracting from {table_name}.{blob_col}: {e}")
                
                conn.close()
                
            except Exception as e:
                print(f"Error processing database {db_file}: {e}")
        
        return extracted_images
    
    def save_extracted_image(self, blob_content: bytes, db_file: Path, 
                           table_name: str, column_name: str, row_id: int) -> Dict[str, Any]:
        """Save extracted binary content as image file"""
        try:
            # Generate unique filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{db_file.stem}_{table_name}_{column_name}_{row_id}_{timestamp}"
            
            # Try to determine file type from content
            file_extension = self.detect_file_type(blob_content)
            full_filename = f"{filename}.{file_extension}"
            
            # Save file
            image_path = self.images_dir / full_filename
            with open(image_path, 'wb') as f:
                f.write(blob_content)
            
            # Calculate hash
            file_hash = hashlib.sha256(blob_content).hexdigest()
            
            # Create evidence record
            evidence_id = f"IMG_{timestamp}_{row_id}"
            image_info = {
                'image_id': evidence_id,
                'evidence_id': evidence_id,
                'image_name': full_filename,
                'image_path': str(image_path),
                'image_format': file_extension,
                'image_size_bytes': len(blob_content),
                'image_hash': file_hash,
                'extraction_method': 'Database BLOB extraction',
                'source_database': str(db_file),
                'source_table': table_name,
                'source_column': column_name,
                'timestamp_extracted': datetime.now().isoformat()
            }
            
            # Store in forensic database
            self.store_image_evidence(image_info)
            
            return image_info
            
        except Exception as e:
            print(f"Error saving extracted content: {e}")
            return None
    
    def detect_file_type(self, content: bytes) -> str:
        """Detect file type from binary content"""
        if content.startswith(b'\xFF\xD8\xFF'):
            return 'jpg'
        elif content.startswith(b'\x89PNG'):
            return 'png'
        elif content.startswith(b'GIF8'):
            return 'gif'
        elif content.startswith(b'BM'):
            return 'bmp'
        elif content.startswith(b'RIFF') and b'WEBP' in content[:12]:
            return 'webp'
        elif content.startswith(b'%PDF'):
            return 'pdf'
        elif content.startswith(b'PK'):
            return 'zip'
        else:
            return 'bin'  # Binary file
    
    def store_image_evidence(self, image_info: Dict[str, Any]):
        """Store image evidence in forensic database"""
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        # Store in evidence catalog
        cursor.execute('''
        INSERT OR REPLACE INTO evidence_catalog 
        (evidence_id, evidence_type, source_database, source_table, description,
         file_path, file_hash_sha256, file_size_bytes, created_timestamp,
         collected_by, legal_relevance, admissibility_status, section_65b_compliance)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            image_info['evidence_id'],
            'Digital Image',
            image_info['source_database'],
            image_info['source_table'],
            f"Image extracted from {image_info['source_table']}.{image_info['source_column']}",
            image_info['image_path'],
            image_info['image_hash'],
            image_info['image_size_bytes'],
            image_info['timestamp_extracted'],
            'Automated Forensic System',
            'Potential digital evidence requiring expert analysis',
            'Pending verification',
            'Requires Section 65B certificate'
        ))
        
        # Store in digital images table
        cursor.execute('''
        INSERT OR REPLACE INTO digital_images 
        (image_id, evidence_id, image_name, image_path, image_format,
         image_size_bytes, image_hash, extraction_method, timestamp_extracted)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            image_info['image_id'],
            image_info['evidence_id'],
            image_info['image_name'],
            image_info['image_path'],
            image_info['image_format'],
            image_info['image_size_bytes'],
            image_info['image_hash'],
            image_info['extraction_method'],
            image_info['timestamp_extracted']
        ))
        
        conn.commit()
        conn.close()
    
    def create_chain_of_custody_entry(self, evidence_id: str, handler_name: str, 
                                    action: str, purpose: str = "Forensic Analysis"):
        """Create chain of custody entry"""
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        custody_id = f"COC_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{evidence_id}"
        
        cursor.execute('''
        INSERT INTO chain_of_custody 
        (custody_id, evidence_id, handler_name, handler_designation, action_taken,
         timestamp, location, purpose, digital_signature)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            custody_id,
            evidence_id,
            handler_name,
            'Digital Forensic Analyst',
            action,
            datetime.now().isoformat(),
            str(self.workspace_path),
            purpose,
            hashlib.sha256(f"{custody_id}{evidence_id}{action}".encode()).hexdigest()[:16]
        ))
        
        conn.commit()
        conn.close()
    
    def generate_comprehensive_report(self) -> str:
        """Generate comprehensive forensic report"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.reports_dir / f"comprehensive_forensic_report_{timestamp}.json"
        
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        # Collect all evidence
        cursor.execute("SELECT * FROM evidence_catalog")
        evidence_records = cursor.fetchall()
        
        cursor.execute("SELECT * FROM digital_images")
        image_records = cursor.fetchall()
        
        cursor.execute("SELECT * FROM legal_procedures")
        procedure_records = cursor.fetchall()
        
        cursor.execute("SELECT * FROM chain_of_custody")
        custody_records = cursor.fetchall()
        
        conn.close()
        
        # Create comprehensive report
        report = {
            'report_metadata': {
                'report_id': f"FORENSIC_REPORT_{timestamp}",
                'generation_timestamp': datetime.now().isoformat(),
                'report_type': 'Comprehensive Forensic Database Analysis',
                'legal_compliance': 'Indian Evidence Act 1872, BSA 2023, BNSS 2023',
                'total_evidence_items': len(evidence_records),
                'total_images_extracted': len(image_records),
                'workspace_path': str(self.workspace_path)
            },
            'evidence_catalog': [dict(zip([col[0] for col in cursor.description], row)) 
                               for row in evidence_records] if evidence_records else [],
            'digital_images': [dict(zip([col[0] for col in cursor.description], row)) 
                             for row in image_records] if image_records else [],
            'legal_procedures': [dict(zip([col[0] for col in cursor.description], row)) 
                               for row in procedure_records] if procedure_records else [],
            'chain_of_custody': [dict(zip([col[0] for col in cursor.description], row)) 
                               for row in custody_records] if custody_records else [],
            'legal_certification': {
                'section_65b_compliance': 'All digital evidence cataloged for Section 65B compliance',
                'chain_of_custody_maintained': 'Complete chain of custody documented',
                'integrity_verification': 'SHA-256 hashes calculated for all evidence',
                'admissibility_status': 'Evidence prepared for legal admissibility review'
            }
        }
        
        # Save report
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return str(report_file)
    
    def run_complete_forensic_analysis(self) -> Dict[str, str]:
        """Run complete forensic analysis process"""
        print("🔍 Starting Comprehensive Forensic Analysis...")
        print("=" * 60)
        
        # Step 1: Extract images from databases
        print("📸 Extracting images from databases...")
        extracted_images = self.extract_images_from_databases()
        print(f"✅ Extracted {len(extracted_images)} images")
        
        # Step 2: Create chain of custody entries
        print("📋 Creating chain of custody entries...")
        for image in extracted_images:
            self.create_chain_of_custody_entry(
                image['evidence_id'], 
                'Forensic System', 
                'Image extracted and cataloged'
            )
        
        # Step 3: Generate comprehensive report
        print("📄 Generating comprehensive forensic report...")
        report_file = self.generate_comprehensive_report()
        
        print("✅ Comprehensive forensic analysis complete!")
        
        return {
            'forensic_database': str(self.forensic_db_path),
            'evidence_directory': str(self.evidence_dir),
            'comprehensive_report': report_file,
            'images_extracted': len(extracted_images)
        }

def main():
    """Main function to run comprehensive forensic system"""
    workspace = r"c:\Users\<USER>\Desktop\New folder"
    
    system = ComprehensiveForensicSystem(workspace)
    results = system.run_complete_forensic_analysis()
    
    print("\n📊 FORENSIC ANALYSIS RESULTS:")
    print("=" * 40)
    for key, value in results.items():
        print(f"{key}: {value}")
    
    return results

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Forensic SQLite Database Creator
Creates a comprehensive forensic database compliant with Indian legal standards
Consolidates all database analysis, evidence catalog, and legal procedures
"""

import sqlite3
import json
import hashlib
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple

class ForensicSQLiteCreator:
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path)
        self.forensic_db_path = self.workspace_path / "MASTER_FORENSIC_DATABASE.db"
        self.analysis_file = self.workspace_path / "forensic_analysis_report.json"
        
        # Load existing analysis data
        self.analysis_data = self.load_analysis_data()
        
    def load_analysis_data(self) -> Dict[str, Any]:
        """Load existing forensic analysis data"""
        try:
            if self.analysis_file.exists():
                with open(self.analysis_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load analysis data: {e}")
        return {}
    
    def create_forensic_database(self):
        """Create comprehensive forensic SQLite database"""
        print("🔍 Creating Master Forensic SQLite Database...")
        
        # Remove existing database if it exists
        if self.forensic_db_path.exists():
            self.forensic_db_path.unlink()
        
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        # Enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Create all forensic tables
        self.create_case_management_tables(cursor)
        self.create_evidence_tables(cursor)
        self.create_database_analysis_tables(cursor)
        self.create_legal_compliance_tables(cursor)
        self.create_chain_of_custody_tables(cursor)
        self.create_digital_forensics_tables(cursor)
        self.create_reporting_tables(cursor)
        self.create_audit_tables(cursor)
        
        # Insert initial data
        self.populate_initial_data(cursor)
        
        # Create indexes for performance
        self.create_indexes(cursor)
        
        # Create views for reporting
        self.create_views(cursor)
        
        # Create triggers for audit trail
        self.create_triggers(cursor)
        
        conn.commit()
        conn.close()
        
        print(f"✅ Master Forensic Database created: {self.forensic_db_path}")
        return str(self.forensic_db_path)
    
    def create_case_management_tables(self, cursor):
        """Create case management tables"""
        
        # Main case information
        cursor.execute('''
        CREATE TABLE cases (
            case_id TEXT PRIMARY KEY,
            case_number TEXT UNIQUE NOT NULL,
            case_title TEXT NOT NULL,
            case_type TEXT NOT NULL,
            investigating_officer TEXT,
            io_designation TEXT,
            io_badge_number TEXT,
            police_station TEXT,
            district TEXT,
            state TEXT,
            fir_number TEXT,
            fir_date TEXT,
            case_status TEXT DEFAULT 'ACTIVE',
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            legal_sections TEXT, -- JSON array of applicable sections
            case_priority TEXT DEFAULT 'MEDIUM',
            case_category TEXT,
            supervising_officer TEXT
        )''')
        
        # Case participants
        cursor.execute('''
        CREATE TABLE case_participants (
            participant_id TEXT PRIMARY KEY,
            case_id TEXT NOT NULL,
            participant_type TEXT NOT NULL, -- ACCUSED, VICTIM, WITNESS, COMPLAINANT
            name TEXT NOT NULL,
            father_name TEXT,
            address TEXT,
            phone_number TEXT,
            email TEXT,
            aadhar_number TEXT,
            age INTEGER,
            gender TEXT,
            occupation TEXT,
            role_description TEXT,
            statement_recorded TEXT DEFAULT 'NO',
            statement_date TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (case_id) REFERENCES cases (case_id)
        )''')
        
        # Case timeline
        cursor.execute('''
        CREATE TABLE case_timeline (
            timeline_id TEXT PRIMARY KEY,
            case_id TEXT NOT NULL,
            event_date TEXT NOT NULL,
            event_time TEXT,
            event_type TEXT NOT NULL,
            event_description TEXT NOT NULL,
            location TEXT,
            officer_name TEXT,
            evidence_collected TEXT,
            remarks TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (case_id) REFERENCES cases (case_id)
        )''')
    
    def create_evidence_tables(self, cursor):
        """Create evidence management tables"""
        
        # Main evidence catalog
        cursor.execute('''
        CREATE TABLE evidence_catalog (
            evidence_id TEXT PRIMARY KEY,
            case_id TEXT NOT NULL,
            evidence_number TEXT UNIQUE NOT NULL,
            evidence_type TEXT NOT NULL,
            evidence_category TEXT NOT NULL,
            description TEXT NOT NULL,
            source_location TEXT,
            collection_date TEXT NOT NULL,
            collection_time TEXT,
            collected_by TEXT NOT NULL,
            collector_designation TEXT,
            witness_1 TEXT,
            witness_2 TEXT,
            seizure_memo_number TEXT,
            physical_location TEXT,
            storage_condition TEXT,
            chain_of_custody_status TEXT DEFAULT 'ACTIVE',
            legal_relevance TEXT,
            admissibility_status TEXT DEFAULT 'PENDING',
            section_65b_compliance TEXT DEFAULT 'PENDING',
            file_path TEXT,
            file_hash_md5 TEXT,
            file_hash_sha1 TEXT,
            file_hash_sha256 TEXT,
            file_size_bytes INTEGER,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (case_id) REFERENCES cases (case_id)
        )''')
        
        # Digital evidence specific details
        cursor.execute('''
        CREATE TABLE digital_evidence (
            digital_evidence_id TEXT PRIMARY KEY,
            evidence_id TEXT NOT NULL,
            device_type TEXT,
            device_make TEXT,
            device_model TEXT,
            device_serial_number TEXT,
            device_imei TEXT,
            operating_system TEXT,
            file_system TEXT,
            storage_capacity TEXT,
            acquisition_method TEXT,
            acquisition_tool TEXT,
            acquisition_date TEXT,
            acquisition_time TEXT,
            image_format TEXT,
            image_verification_hash TEXT,
            write_blocker_used TEXT DEFAULT 'YES',
            forensic_examiner TEXT,
            examination_date TEXT,
            examination_report_path TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (evidence_id) REFERENCES evidence_catalog (evidence_id)
        )''')
        
        # Evidence images and attachments
        cursor.execute('''
        CREATE TABLE evidence_attachments (
            attachment_id TEXT PRIMARY KEY,
            evidence_id TEXT NOT NULL,
            attachment_type TEXT NOT NULL, -- PHOTO, DOCUMENT, VIDEO, AUDIO
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_size_bytes INTEGER,
            file_hash TEXT,
            mime_type TEXT,
            description TEXT,
            captured_by TEXT,
            captured_date TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (evidence_id) REFERENCES evidence_catalog (evidence_id)
        )''')
    
    def create_database_analysis_tables(self, cursor):
        """Create database analysis tables"""
        
        # Database files analyzed
        cursor.execute('''
        CREATE TABLE analyzed_databases (
            db_analysis_id TEXT PRIMARY KEY,
            case_id TEXT,
            database_path TEXT NOT NULL,
            database_name TEXT NOT NULL,
            database_type TEXT,
            file_size_bytes INTEGER,
            file_size_mb REAL,
            file_hash_md5 TEXT,
            file_hash_sha1 TEXT,
            file_hash_sha256 TEXT,
            created_date TEXT,
            modified_date TEXT,
            accessed_date TEXT,
            analysis_date TEXT NOT NULL,
            analysis_status TEXT DEFAULT 'COMPLETED',
            total_tables INTEGER DEFAULT 0,
            total_records INTEGER DEFAULT 0,
            analysis_notes TEXT,
            examiner_name TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (case_id) REFERENCES cases (case_id)
        )''')
        
        # Database tables analysis
        cursor.execute('''
        CREATE TABLE database_tables (
            table_id TEXT PRIMARY KEY,
            db_analysis_id TEXT NOT NULL,
            table_name TEXT NOT NULL,
            table_type TEXT DEFAULT 'TABLE',
            column_count INTEGER DEFAULT 0,
            record_count INTEGER DEFAULT 0,
            table_schema TEXT, -- JSON representation of table structure
            creation_sql TEXT,
            data_types TEXT, -- JSON array of column data types
            primary_keys TEXT, -- JSON array of primary key columns
            foreign_keys TEXT, -- JSON array of foreign key relationships
            indexes TEXT, -- JSON array of indexes
            triggers TEXT, -- JSON array of triggers
            constraints TEXT, -- JSON array of constraints
            sample_data TEXT, -- JSON sample of first few records
            analysis_notes TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (db_analysis_id) REFERENCES analyzed_databases (db_analysis_id)
        )''')
        
        # Extracted data records
        cursor.execute('''
        CREATE TABLE extracted_records (
            record_id TEXT PRIMARY KEY,
            table_id TEXT NOT NULL,
            source_row_id INTEGER,
            record_data TEXT NOT NULL, -- JSON representation of the record
            record_hash TEXT,
            extraction_date TEXT NOT NULL,
            data_classification TEXT, -- PERSONAL, SENSITIVE, PUBLIC, CONFIDENTIAL
            legal_relevance TEXT,
            extraction_method TEXT,
            verified_status TEXT DEFAULT 'PENDING',
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (table_id) REFERENCES database_tables (table_id)
        )''')
    
    def create_legal_compliance_tables(self, cursor):
        """Create legal compliance and procedure tables"""
        
        # Legal procedures and requirements
        cursor.execute('''
        CREATE TABLE legal_procedures (
            procedure_id TEXT PRIMARY KEY,
            procedure_name TEXT NOT NULL,
            legal_section TEXT NOT NULL,
            act_reference TEXT NOT NULL,
            procedure_category TEXT,
            procedure_steps TEXT, -- JSON array of steps
            compliance_checklist TEXT, -- JSON array of checklist items
            required_documentation TEXT,
            timeline_requirements TEXT,
            responsible_authority TEXT,
            penalty_for_non_compliance TEXT,
            precedent_cases TEXT, -- JSON array of relevant case laws
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_timestamp TEXT DEFAULT CURRENT_TIMESTAMP
        )''')
        
        # Compliance tracking
        cursor.execute('''
        CREATE TABLE compliance_tracking (
            compliance_id TEXT PRIMARY KEY,
            case_id TEXT,
            evidence_id TEXT,
            procedure_id TEXT NOT NULL,
            compliance_status TEXT NOT NULL, -- COMPLIANT, NON_COMPLIANT, PENDING, PARTIAL
            verification_method TEXT,
            verifier_name TEXT,
            verifier_designation TEXT,
            verification_date TEXT,
            compliance_notes TEXT,
            remedial_action_required TEXT,
            remedial_action_taken TEXT,
            compliance_certificate_path TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (case_id) REFERENCES cases (case_id),
            FOREIGN KEY (evidence_id) REFERENCES evidence_catalog (evidence_id),
            FOREIGN KEY (procedure_id) REFERENCES legal_procedures (procedure_id)
        )''')
        
        # Section 65B certificates
        cursor.execute('''
        CREATE TABLE section_65b_certificates (
            certificate_id TEXT PRIMARY KEY,
            evidence_id TEXT NOT NULL,
            case_id TEXT NOT NULL,
            certificate_number TEXT UNIQUE NOT NULL,
            computer_system_description TEXT NOT NULL,
            regular_use_period TEXT NOT NULL,
            information_source TEXT NOT NULL,
            computer_operating_properly TEXT NOT NULL,
            certificate_issuer_name TEXT NOT NULL,
            certificate_issuer_designation TEXT NOT NULL,
            certificate_date TEXT NOT NULL,
            certificate_place TEXT NOT NULL,
            certificate_file_path TEXT,
            digital_signature TEXT,
            verification_status TEXT DEFAULT 'PENDING',
            court_acceptance_status TEXT DEFAULT 'PENDING',
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (evidence_id) REFERENCES evidence_catalog (evidence_id),
            FOREIGN KEY (case_id) REFERENCES cases (case_id)
        )''')
    
    def create_chain_of_custody_tables(self, cursor):
        """Create chain of custody tables"""
        
        # Chain of custody main table
        cursor.execute('''
        CREATE TABLE chain_of_custody (
            custody_id TEXT PRIMARY KEY,
            evidence_id TEXT NOT NULL,
            sequence_number INTEGER NOT NULL,
            handler_name TEXT NOT NULL,
            handler_designation TEXT NOT NULL,
            handler_badge_number TEXT,
            action_taken TEXT NOT NULL,
            action_date TEXT NOT NULL,
            action_time TEXT NOT NULL,
            location TEXT NOT NULL,
            purpose TEXT NOT NULL,
            condition_received TEXT,
            condition_transferred TEXT,
            witness_name TEXT,
            witness_designation TEXT,
            next_handler TEXT,
            transfer_method TEXT,
            security_measures TEXT,
            digital_signature TEXT,
            physical_signature_path TEXT,
            remarks TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (evidence_id) REFERENCES evidence_catalog (evidence_id)
        )''')
        
        # Chain of custody verification
        cursor.execute('''
        CREATE TABLE custody_verification (
            verification_id TEXT PRIMARY KEY,
            custody_id TEXT NOT NULL,
            verification_type TEXT NOT NULL, -- HASH_VERIFICATION, PHYSICAL_INSPECTION, DIGITAL_SIGNATURE
            verification_result TEXT NOT NULL, -- VERIFIED, FAILED, INCONCLUSIVE
            verification_date TEXT NOT NULL,
            verifier_name TEXT NOT NULL,
            verification_method TEXT,
            verification_details TEXT,
            discrepancy_noted TEXT,
            corrective_action TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (custody_id) REFERENCES chain_of_custody (custody_id)
        )''')
    
    def create_digital_forensics_tables(self, cursor):
        """Create digital forensics specific tables"""
        
        # Forensic examination details
        cursor.execute('''
        CREATE TABLE forensic_examinations (
            examination_id TEXT PRIMARY KEY,
            evidence_id TEXT NOT NULL,
            case_id TEXT NOT NULL,
            examination_type TEXT NOT NULL, -- MOBILE, COMPUTER, NETWORK, DATABASE
            examiner_name TEXT NOT NULL,
            examiner_certification TEXT,
            examination_start_date TEXT NOT NULL,
            examination_end_date TEXT,
            examination_status TEXT DEFAULT 'IN_PROGRESS',
            tools_used TEXT, -- JSON array of forensic tools
            methodology TEXT,
            findings_summary TEXT,
            artifacts_recovered INTEGER DEFAULT 0,
            examination_report_path TEXT,
            technical_notes TEXT,
            challenges_faced TEXT,
            recommendations TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (evidence_id) REFERENCES evidence_catalog (evidence_id),
            FOREIGN KEY (case_id) REFERENCES cases (case_id)
        )''')
        
        # Digital artifacts
        cursor.execute('''
        CREATE TABLE digital_artifacts (
            artifact_id TEXT PRIMARY KEY,
            examination_id TEXT NOT NULL,
            artifact_type TEXT NOT NULL, -- FILE, REGISTRY, LOG, METADATA, DELETED_DATA
            artifact_name TEXT NOT NULL,
            artifact_path TEXT,
            artifact_size_bytes INTEGER,
            artifact_hash TEXT,
            creation_date TEXT,
            modification_date TEXT,
            access_date TEXT,
            file_signature TEXT,
            metadata_json TEXT,
            recovery_method TEXT,
            legal_significance TEXT,
            analysis_notes TEXT,
            extracted_content_path TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (examination_id) REFERENCES forensic_examinations (examination_id)
        )''')
        
        # Network forensics
        cursor.execute('''
        CREATE TABLE network_forensics (
            network_id TEXT PRIMARY KEY,
            examination_id TEXT NOT NULL,
            capture_date TEXT NOT NULL,
            capture_duration TEXT,
            network_interface TEXT,
            capture_file_path TEXT,
            capture_file_size_bytes INTEGER,
            capture_file_hash TEXT,
            protocol_analysis TEXT, -- JSON of protocol statistics
            suspicious_activities TEXT, -- JSON array of suspicious activities
            ip_addresses TEXT, -- JSON array of IP addresses
            domain_names TEXT, -- JSON array of domain names
            communication_patterns TEXT,
            analysis_tools_used TEXT,
            findings TEXT,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (examination_id) REFERENCES forensic_examinations (examination_id)
        )''')
    
    def create_reporting_tables(self, cursor):
        """Create reporting and documentation tables"""
        
        # Forensic reports
        cursor.execute('''
        CREATE TABLE forensic_reports (
            report_id TEXT PRIMARY KEY,
            case_id TEXT NOT NULL,
            report_type TEXT NOT NULL, -- PRELIMINARY, INTERIM, FINAL, SUPPLEMENTARY
            report_title TEXT NOT NULL,
            report_date TEXT NOT NULL,
            prepared_by TEXT NOT NULL,
            reviewed_by TEXT,
            approved_by TEXT,
            report_status TEXT DEFAULT 'DRAFT',
            report_file_path TEXT,
            report_hash TEXT,
            executive_summary TEXT,
            methodology TEXT,
            findings TEXT,
            conclusions TEXT,
            recommendations TEXT,
            limitations TEXT,
            appendices_count INTEGER DEFAULT 0,
            page_count INTEGER,
            submission_date TEXT,
            court_submission_status TEXT DEFAULT 'PENDING',
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (case_id) REFERENCES cases (case_id)
        )''')
        
        # Report sections
        cursor.execute('''
        CREATE TABLE report_sections (
            section_id TEXT PRIMARY KEY,
            report_id TEXT NOT NULL,
            section_number TEXT NOT NULL,
            section_title TEXT NOT NULL,
            section_content TEXT NOT NULL,
            section_type TEXT, -- TEXT, TABLE, IMAGE, CHART
            page_number INTEGER,
            word_count INTEGER,
            created_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (report_id) REFERENCES forensic_reports (report_id)
        )''')
    
    def create_audit_tables(self, cursor):
        """Create audit and logging tables"""
        
        # System audit log
        cursor.execute('''
        CREATE TABLE audit_log (
            audit_id TEXT PRIMARY KEY,
            table_name TEXT NOT NULL,
            record_id TEXT NOT NULL,
            action_type TEXT NOT NULL, -- INSERT, UPDATE, DELETE, SELECT
            old_values TEXT, -- JSON of old values
            new_values TEXT, -- JSON of new values
            user_name TEXT NOT NULL,
            user_role TEXT,
            ip_address TEXT,
            timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            session_id TEXT,
            application_name TEXT DEFAULT 'FORENSIC_DATABASE_SYSTEM'
        )''')
        
        # Access log
        cursor.execute('''
        CREATE TABLE access_log (
            access_id TEXT PRIMARY KEY,
            user_name TEXT NOT NULL,
            access_type TEXT NOT NULL, -- LOGIN, LOGOUT, QUERY, EXPORT
            resource_accessed TEXT,
            access_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            ip_address TEXT,
            user_agent TEXT,
            success_status TEXT NOT NULL, -- SUCCESS, FAILED
            failure_reason TEXT,
            session_duration INTEGER -- in seconds
        )''')
    
    def create_indexes(self, cursor):
        """Create database indexes for performance"""
        indexes = [
            "CREATE INDEX idx_cases_case_number ON cases(case_number)",
            "CREATE INDEX idx_cases_fir_number ON cases(fir_number)",
            "CREATE INDEX idx_evidence_case_id ON evidence_catalog(case_id)",
            "CREATE INDEX idx_evidence_collection_date ON evidence_catalog(collection_date)",
            "CREATE INDEX idx_evidence_type ON evidence_catalog(evidence_type)",
            "CREATE INDEX idx_chain_custody_evidence_id ON chain_of_custody(evidence_id)",
            "CREATE INDEX idx_chain_custody_date ON chain_of_custody(action_date)",
            "CREATE INDEX idx_compliance_case_id ON compliance_tracking(case_id)",
            "CREATE INDEX idx_compliance_status ON compliance_tracking(compliance_status)",
            "CREATE INDEX idx_db_analysis_case_id ON analyzed_databases(case_id)",
            "CREATE INDEX idx_db_tables_analysis_id ON database_tables(db_analysis_id)",
            "CREATE INDEX idx_extracted_records_table_id ON extracted_records(table_id)",
            "CREATE INDEX idx_forensic_exam_case_id ON forensic_examinations(case_id)",
            "CREATE INDEX idx_digital_artifacts_exam_id ON digital_artifacts(examination_id)",
            "CREATE INDEX idx_reports_case_id ON forensic_reports(case_id)",
            "CREATE INDEX idx_audit_timestamp ON audit_log(timestamp)",
            "CREATE INDEX idx_access_timestamp ON access_log(access_timestamp)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def create_views(self, cursor):
        """Create database views for reporting"""
        
        # Case summary view
        cursor.execute('''
        CREATE VIEW case_summary AS
        SELECT 
            c.case_id,
            c.case_number,
            c.case_title,
            c.case_type,
            c.investigating_officer,
            c.police_station,
            c.case_status,
            COUNT(DISTINCT e.evidence_id) as total_evidence,
            COUNT(DISTINCT da.db_analysis_id) as databases_analyzed,
            COUNT(DISTINCT fe.examination_id) as forensic_examinations,
            COUNT(DISTINCT fr.report_id) as reports_generated
        FROM cases c
        LEFT JOIN evidence_catalog e ON c.case_id = e.case_id
        LEFT JOIN analyzed_databases da ON c.case_id = da.case_id
        LEFT JOIN forensic_examinations fe ON c.case_id = fe.case_id
        LEFT JOIN forensic_reports fr ON c.case_id = fr.case_id
        GROUP BY c.case_id
        ''')
        
        # Evidence status view
        cursor.execute('''
        CREATE VIEW evidence_status AS
        SELECT 
            e.evidence_id,
            e.evidence_number,
            e.evidence_type,
            e.collection_date,
            e.admissibility_status,
            e.section_65b_compliance,
            COUNT(coc.custody_id) as custody_transfers,
            MAX(coc.action_date) as last_custody_date,
            ct.compliance_status as legal_compliance_status
        FROM evidence_catalog e
        LEFT JOIN chain_of_custody coc ON e.evidence_id = coc.evidence_id
        LEFT JOIN compliance_tracking ct ON e.evidence_id = ct.evidence_id
        GROUP BY e.evidence_id
        ''')
        
        # Database analysis summary view
        cursor.execute('''
        CREATE VIEW database_analysis_summary AS
        SELECT 
            da.db_analysis_id,
            da.database_name,
            da.file_size_mb,
            da.analysis_date,
            COUNT(dt.table_id) as total_tables,
            SUM(dt.record_count) as total_records,
            COUNT(er.record_id) as extracted_records
        FROM analyzed_databases da
        LEFT JOIN database_tables dt ON da.db_analysis_id = dt.db_analysis_id
        LEFT JOIN extracted_records er ON dt.table_id = er.table_id
        GROUP BY da.db_analysis_id
        ''')
    
    def create_triggers(self, cursor):
        """Create audit triggers"""
        
        # Audit trigger for evidence_catalog
        cursor.execute('''
        CREATE TRIGGER audit_evidence_catalog
        AFTER UPDATE ON evidence_catalog
        FOR EACH ROW
        BEGIN
            INSERT INTO audit_log (
                audit_id, table_name, record_id, action_type, 
                old_values, new_values, user_name, timestamp
            ) VALUES (
                'AUD_' || datetime('now') || '_' || NEW.evidence_id,
                'evidence_catalog',
                NEW.evidence_id,
                'UPDATE',
                json_object('admissibility_status', OLD.admissibility_status, 'section_65b_compliance', OLD.section_65b_compliance),
                json_object('admissibility_status', NEW.admissibility_status, 'section_65b_compliance', NEW.section_65b_compliance),
                'SYSTEM',
                datetime('now')
            );
        END
        ''')
        
        # Update timestamp trigger
        cursor.execute('''
        CREATE TRIGGER update_evidence_timestamp
        AFTER UPDATE ON evidence_catalog
        FOR EACH ROW
        BEGIN
            UPDATE evidence_catalog 
            SET updated_timestamp = datetime('now')
            WHERE evidence_id = NEW.evidence_id;
        END
        ''')
    
    def populate_initial_data(self, cursor):
        """Populate database with initial data from analysis"""
        
        # Insert standard legal procedures
        self.insert_legal_procedures(cursor)
        
        # Insert analysis data if available
        if self.analysis_data:
            self.insert_analysis_data(cursor)
    
    def insert_legal_procedures(self, cursor):
        """Insert standard Indian legal procedures"""
        procedures = [
            {
                'procedure_id': 'IEA_65B_COMPLIANCE',
                'procedure_name': 'Section 65B Indian Evidence Act Compliance',
                'legal_section': 'Section 65B',
                'act_reference': 'Indian Evidence Act, 1872',
                'procedure_category': 'DIGITAL_EVIDENCE',
                'procedure_steps': json.dumps([
                    "1. Identify the electronic device/system containing the evidence",
                    "2. Ensure the device was in regular use during the relevant period",
                    "3. Verify that information was regularly fed into the computer",
                    "4. Confirm the computer was operating properly",
                    "5. Ensure the information derives from the original source",
                    "6. Obtain certificate under Section 65B(4)",
                    "7. Document the chain of custody",
                    "8. Preserve original evidence without modification"
                ]),
                'compliance_checklist': json.dumps([
                    "Certificate under Section 65B(4) obtained",
                    "Chain of custody documented",
                    "Hash values calculated and verified",
                    "Original evidence preserved",
                    "Proper authorization obtained",
                    "Expert witness identified if required"
                ]),
                'required_documentation': 'Section 65B Certificate, Chain of Custody, Hash Verification Report',
                'timeline_requirements': 'Evidence must be collected and certified within reasonable time',
                'responsible_authority': 'Investigating Officer / Forensic Expert',
                'penalty_for_non_compliance': 'Evidence may be inadmissible in court',
                'precedent_cases': json.dumps([
                    "Anvar P.V. v. P.K. Basheer (2014) 10 SCC 473",
                    "Arjun Panditrao Khotkar v. Kailash Kushanrao Gorantyal (2020) 7 SCC 1",
                    "Shafhi Mohammad v. State of Himachal Pradesh (2018) 2 SCC 801"
                ])
            },
            {
                'procedure_id': 'BSA_2023_DIGITAL_EVIDENCE',
                'procedure_name': 'Bharatiya Sakshya Adhiniyam 2023 Digital Evidence',
                'legal_section': 'Section 63',
                'act_reference': 'Bharatiya Sakshya Adhiniyam, 2023',
                'procedure_category': 'DIGITAL_EVIDENCE',
                'procedure_steps': json.dumps([
                    "1. Identify electronic or digital record",
                    "2. Ensure proper authentication of digital evidence",
                    "3. Verify integrity through hash functions",
                    "4. Document the source and method of collection",
                    "5. Maintain chain of custody",
                    "6. Obtain necessary certificates",
                    "7. Ensure compliance with procedural requirements"
                ]),
                'compliance_checklist': json.dumps([
                    "Digital record properly authenticated",
                    "Integrity verified through cryptographic methods",
                    "Source documentation complete",
                    "Chain of custody maintained",
                    "Procedural compliance verified"
                ]),
                'required_documentation': 'Authentication Certificate, Integrity Verification, Chain of Custody',
                'timeline_requirements': 'As per investigation timeline',
                'responsible_authority': 'Investigating Officer / Digital Forensic Expert',
                'penalty_for_non_compliance': 'Evidence inadmissibility under BSA 2023',
                'precedent_cases': json.dumps([
                    "BSA 2023 implementation guidelines",
                    "Digital evidence standards under new criminal laws"
                ])
            },
            {
                'procedure_id': 'BNSS_2023_SEARCH_SEIZURE',
                'procedure_name': 'BNSS 2023 Search and Seizure of Digital Evidence',
                'legal_section': 'Section 93, 100',
                'act_reference': 'Bharatiya Nagarik Suraksha Sanhita, 2023',
                'procedure_category': 'SEARCH_SEIZURE',
                'procedure_steps': json.dumps([
                    "1. Obtain proper search warrant or authorization",
                    "2. Conduct search in presence of witnesses",
                    "3. Prepare detailed seizure memo",
                    "4. Create forensic images of digital devices",
                    "5. Calculate and record hash values",
                    "6. Seal and label evidence properly",
                    "7. Update investigation diary",
                    "8. Submit to Forensic Science Laboratory if required"
                ]),
                'compliance_checklist': json.dumps([
                    "Valid search warrant obtained",
                    "Witnesses present during search",
                    "Seizure memo prepared",
                    "Forensic imaging completed",
                    "Hash values calculated",
                    "Evidence properly sealed",
                    "Investigation diary updated"
                ]),
                'required_documentation': 'Search Warrant, Seizure Memo, Witness Statements, Hash Report',
                'timeline_requirements': 'Search to be completed within authorized timeframe',
                'responsible_authority': 'Investigating Officer with Forensic Expert',
                'penalty_for_non_compliance': 'Evidence may be excluded under BNSS 2023',
                'precedent_cases': json.dumps([
                    "BNSS 2023 procedural guidelines",
                    "Digital search and seizure protocols"
                ])
            }
        ]
        
        for procedure in procedures:
            cursor.execute('''
            INSERT OR REPLACE INTO legal_procedures 
            (procedure_id, procedure_name, legal_section, act_reference, procedure_category,
             procedure_steps, compliance_checklist, required_documentation, 
             timeline_requirements, responsible_authority, penalty_for_non_compliance, precedent_cases)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                procedure['procedure_id'],
                procedure['procedure_name'],
                procedure['legal_section'],
                procedure['act_reference'],
                procedure['procedure_category'],
                procedure['procedure_steps'],
                procedure['compliance_checklist'],
                procedure['required_documentation'],
                procedure['timeline_requirements'],
                procedure['responsible_authority'],
                procedure['penalty_for_non_compliance'],
                procedure['precedent_cases']
            ))
    
    def insert_analysis_data(self, cursor):
        """Insert existing analysis data into forensic database"""
        
        # Create a default case for the analysis
        case_id = f"CASE_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute('''
        INSERT INTO cases (
            case_id, case_number, case_title, case_type, investigating_officer,
            police_station, case_status, legal_sections, case_category
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            case_id,
            f"FIR/{datetime.now().strftime('%Y')}/DIGITAL/001",
            "Digital Database Forensic Analysis",
            "DIGITAL_FORENSICS",
            "Digital Forensic Analyst",
            "Cyber Crime Police Station",
            "UNDER_INVESTIGATION",
            json.dumps(["IPC 420", "IT Act 66", "BSA 2023"]),
            "CYBER_CRIME"
        ))
        
        # Insert database analysis data
        databases = self.analysis_data.get('databases', {})
        db_counter = 0
        for db_path, db_data in databases.items():
            db_counter += 1
            db_analysis_id = f"DBA_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{db_counter:04d}_{abs(hash(db_path)) % 10000}"
            
            metadata = db_data.get('metadata', {})
            
            cursor.execute('''
            INSERT INTO analyzed_databases (
                db_analysis_id, case_id, database_path, database_name, database_type,
                file_size_bytes, file_size_mb, file_hash_md5, file_hash_sha1, file_hash_sha256,
                created_date, modified_date, analysis_date, total_tables, total_records,
                examiner_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                db_analysis_id,
                case_id,
                db_path,
                Path(db_path).name,
                'SQLite',
                metadata.get('size_bytes', 0),
                metadata.get('size_mb', 0),
                metadata.get('hashes', {}).get('md5'),
                metadata.get('hashes', {}).get('sha1'),
                metadata.get('hashes', {}).get('sha256'),
                metadata.get('created_time'),
                metadata.get('modified_time'),
                datetime.now().isoformat(),
                len(db_data.get('tables', {})),
                sum(table.get('row_count', 0) for table in db_data.get('tables', {}).values()),
                'Automated Forensic System'
            ))
            
            # Insert table analysis data
            tables = db_data.get('tables', {})
            table_counter = 0
            for table_name, table_info in tables.items():
                table_counter += 1
                table_id = f"TBL_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{table_counter:04d}_{abs(hash(f'{db_path}_{table_name}')) % 10000}"
                
                cursor.execute('''
                INSERT INTO database_tables (
                    table_id, db_analysis_id, table_name, column_count, record_count,
                    table_schema, creation_sql, primary_keys
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    table_id,
                    db_analysis_id,
                    table_name,
                    len(table_info.get('columns', [])),
                    table_info.get('row_count', 0),
                    json.dumps(table_info.get('columns', [])),
                    table_info.get('creation_sql'),
                    json.dumps([col['name'] for col in table_info.get('columns', []) if col.get('pk')])
                ))
    
    def generate_database_report(self) -> Dict[str, Any]:
        """Generate comprehensive database report"""
        conn = sqlite3.connect(self.forensic_db_path)
        cursor = conn.cursor()
        
        # Get database statistics
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        report = {
            'database_path': str(self.forensic_db_path),
            'creation_timestamp': datetime.now().isoformat(),
            'total_tables': len(tables),
            'database_size_mb': round(self.forensic_db_path.stat().st_size / (1024 * 1024), 2),
            'tables_created': tables,
            'legal_compliance': 'Indian Evidence Act 1872, BSA 2023, BNSS 2023',
            'features': [
                'Complete case management system',
                'Evidence catalog with chain of custody',
                'Digital forensics examination tracking',
                'Legal compliance monitoring',
                'Section 65B certificate management',
                'Comprehensive audit trail',
                'Database analysis integration',
                'Automated reporting capabilities'
            ]
        }
        
        # Get record counts for each table
        table_stats = {}
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                table_stats[table] = count
            except:
                table_stats[table] = 0
        
        report['table_statistics'] = table_stats
        
        conn.close()
        
        # Save report
        report_file = self.workspace_path / "forensic_database_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report

def main():
    """Main function to create forensic SQLite database"""
    try:
        workspace = r"c:\Users\<USER>\Desktop\New folder"

        print("🔍 Creating Comprehensive Forensic SQLite Database...")
        print("=" * 60)

        creator = ForensicSQLiteCreator(workspace)
        db_path = creator.create_forensic_database()
        report = creator.generate_database_report()

        print(f"\n✅ Forensic SQLite Database Created Successfully!")
        print(f"📁 Database Path: {db_path}")
        print(f"📊 Total Tables: {report['total_tables']}")
        print(f"💾 Database Size: {report['database_size_mb']} MB")
        print(f"📋 Features: {len(report['features'])} comprehensive features")

        print(f"\n📈 TABLE STATISTICS:")
        for table, count in report['table_statistics'].items():
            print(f"  {table}: {count:,} records")

        return db_path
    except Exception as e:
        print(f"❌ Error creating forensic database: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Forensic Database Analyzer for Indian Legal System
Comprehensive tool to analyze all database files and generate forensic PDF reports
Compliant with Indian Evidence Act, CrPC, and forensic standards
"""

import sqlite3
import os
import json
import hashlib
import datetime
from pathlib import Path
from typing import Dict, List, Any, Tuple
import base64

class ForensicDatabaseAnalyzer:
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path)
        self.analysis_results = {}
        self.forensic_metadata = {}
        self.chain_of_custody = []
        
    def calculate_file_hash(self, file_path: Path) -> Dict[str, str]:
        """Calculate multiple hashes for forensic integrity"""
        hashes = {}
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                hashes['md5'] = hashlib.md5(content).hexdigest()
                hashes['sha1'] = hashlib.sha1(content).hexdigest()
                hashes['sha256'] = hashlib.sha256(content).hexdigest()
        except Exception as e:
            hashes['error'] = str(e)
        return hashes
    
    def get_file_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Extract comprehensive file metadata for forensic purposes"""
        try:
            stat = file_path.stat()
            return {
                'file_name': file_path.name,
                'file_path': str(file_path),
                'size_bytes': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 6),
                'created_time': datetime.datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified_time': datetime.datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'accessed_time': datetime.datetime.fromtimestamp(stat.st_atime).isoformat(),
                'hashes': self.calculate_file_hash(file_path)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_sqlite_database(self, db_path: Path) -> Dict[str, Any]:
        """Comprehensive SQLite database analysis"""
        analysis = {
            'metadata': self.get_file_metadata(db_path),
            'database_info': {},
            'tables': {},
            'indexes': [],
            'triggers': [],
            'views': [],
            'pragma_info': {},
            'data_summary': {}
        }
        
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Get database schema information
            cursor.execute("SELECT name, type, sql FROM sqlite_master")
            schema_objects = cursor.fetchall()
            
            tables = []
            for name, obj_type, sql in schema_objects:
                if obj_type == 'table':
                    tables.append(name)
                    # Analyze table structure
                    cursor.execute(f"PRAGMA table_info({name})")
                    columns = cursor.fetchall()
                    
                    # Get row count
                    cursor.execute(f"SELECT COUNT(*) FROM {name}")
                    row_count = cursor.fetchone()[0]
                    
                    analysis['tables'][name] = {
                        'columns': [{'id': col[0], 'name': col[1], 'type': col[2], 
                                   'not_null': col[3], 'default': col[4], 'pk': col[5]} 
                                  for col in columns],
                        'row_count': row_count,
                        'creation_sql': sql
                    }
                    
                elif obj_type == 'index':
                    analysis['indexes'].append({'name': name, 'sql': sql})
                elif obj_type == 'trigger':
                    analysis['triggers'].append({'name': name, 'sql': sql})
                elif obj_type == 'view':
                    analysis['views'].append({'name': name, 'sql': sql})
            
            # Get PRAGMA information
            pragma_queries = [
                'user_version', 'schema_version', 'page_size', 'page_count',
                'freelist_count', 'encoding', 'journal_mode', 'synchronous'
            ]
            
            for pragma in pragma_queries:
                try:
                    cursor.execute(f"PRAGMA {pragma}")
                    result = cursor.fetchone()
                    analysis['pragma_info'][pragma] = result[0] if result else None
                except:
                    analysis['pragma_info'][pragma] = 'N/A'
            
            conn.close()
            
        except Exception as e:
            analysis['error'] = str(e)
        
        return analysis
    
    def find_database_files(self) -> List[Path]:
        """Find all database files in the workspace"""
        db_extensions = ['.db', '.sqlite', '.sqlite3']
        db_files = []
        
        for ext in db_extensions:
            db_files.extend(self.workspace_path.rglob(f'*{ext}'))
        
        return sorted(db_files)
    
    def analyze_all_databases(self) -> Dict[str, Any]:
        """Analyze all database files in the workspace"""
        db_files = self.find_database_files()
        
        forensic_report = {
            'analysis_metadata': {
                'timestamp': datetime.datetime.now().isoformat(),
                'analyzer_version': '1.0.0',
                'workspace_path': str(self.workspace_path),
                'total_databases_found': len(db_files),
                'analysis_compliance': 'Indian Evidence Act 1872, CrPC 1973, IT Act 2000'
            },
            'databases': {},
            'summary': {
                'total_size_mb': 0,
                'total_tables': 0,
                'total_records': 0,
                'database_categories': {}
            }
        }
        
        for db_file in db_files:
            print(f"Analyzing: {db_file}")
            analysis = self.analyze_sqlite_database(db_file)
            
            # Add to forensic report
            relative_path = str(db_file.relative_to(self.workspace_path))
            forensic_report['databases'][relative_path] = analysis
            
            # Update summary statistics
            if 'metadata' in analysis and 'size_mb' in analysis['metadata']:
                forensic_report['summary']['total_size_mb'] += analysis['metadata']['size_mb']
            
            if 'tables' in analysis:
                forensic_report['summary']['total_tables'] += len(analysis['tables'])
                for table_name, table_info in analysis['tables'].items():
                    forensic_report['summary']['total_records'] += table_info.get('row_count', 0)
            
            # Categorize databases
            category = self.categorize_database(db_file)
            if category not in forensic_report['summary']['database_categories']:
                forensic_report['summary']['database_categories'][category] = 0
            forensic_report['summary']['database_categories'][category] += 1
        
        return forensic_report
    
    def categorize_database(self, db_path: Path) -> str:
        """Categorize database based on path and name"""
        path_str = str(db_path).lower()
        
        if 'neural' in path_str or 'ai' in path_str:
            return 'Neural Network / AI'
        elif 'police' in path_str or 'operational' in path_str:
            return 'Police Operational'
        elif 'legal' in path_str or 'bns' in path_str or 'ipc' in path_str or 'crpc' in path_str:
            return 'Legal Databases'
        elif 'forensic' in path_str or 'evidence' in path_str:
            return 'Forensic & Evidence'
        elif 'case' in path_str or 'investigation' in path_str:
            return 'Case Management'
        else:
            return 'General'

def main():
    """Main function to run the forensic analysis"""
    try:
        workspace = r"c:\Users\<USER>\Desktop\New folder"

        print("🔍 Starting Forensic Database Analysis...")
        print("=" * 60)

        analyzer = ForensicDatabaseAnalyzer(workspace)
        forensic_report = analyzer.analyze_all_databases()

        # Save the forensic analysis report
        output_file = Path(workspace) / "forensic_analysis_report.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(forensic_report, f, indent=2, ensure_ascii=False)

        print(f"\n✅ Forensic analysis complete!")
        print(f"📊 Total databases analyzed: {forensic_report['analysis_metadata']['total_databases_found']}")
        print(f"📁 Total size: {forensic_report['summary']['total_size_mb']:.2f} MB")
        print(f"🗃️ Total tables: {forensic_report['summary']['total_tables']}")
        print(f"📝 Total records: {forensic_report['summary']['total_records']:,}")
        print(f"💾 Report saved to: {output_file}")

        return forensic_report
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
